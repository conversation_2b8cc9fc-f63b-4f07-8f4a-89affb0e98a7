{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "RealityTap Haptics Studio", "version": "1.0.7", "identifier": "com.awa.realitytap.desktop", "build": {"frontendDist": ["../dist", "../public"], "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npx vite build"}, "app": {"withGlobalTauri": true, "windows": [{"title": "AWA RealityTap Studio", "width": 1280, "height": 980, "minWidth": 1280, "minHeight": 980, "resizable": true, "fullscreen": false, "maximized": false, "center": true, "decorations": false, "dragDropEnabled": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "msi", "createUpdaterArtifacts": true, "resources": ["libs/windows/x64/librtcore.dll", "libs/windows/x64/librtssl.dll", "libs/windows/x64/librtutils.dll", "libs/windows/x64/libgcc_s_seh-1.dll", "libs/windows/x64/libstdc++-6.dll", "libs/windows/x64/libwinpthread-1.dll", "motors/", "ffmpeg.exe", "ffprobe.exe"], "icon": ["icons/icon.icns", "icons/icon.ico"]}, "plugins": {"updater": {"active": true, "endpoints": ["http://**************:5000/api/v1/updates/{{target}}/{{current_version}}"], "dialog": false, "dangerousInsecureTransportProtocol": true, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDZBMjY3MEU2RTcwQjIyMEUKUldRT0lndm41bkFtYWhMUXhWSTI3K2haNVBLMEd3dXJGb1F1ZVdmaEdjUG4vV3MzaVJkNWVpR3oK", "windows": {"installMode": "passive"}}}}