{"rustc": 10895048813736897673, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 5408242616063297496, "profile": 16865373277404624699, "path": 16206831015564736602, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-5626c7a38a4fcca6\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}