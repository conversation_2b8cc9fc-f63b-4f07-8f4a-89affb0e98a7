use std::env;
use std::fs;
use std::path::Path;

fn main() {
    let build_date = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string();
    println!("cargo:rustc-env=BUILD_DATE={}", build_date);

    // 复制预编译的 ffmpeg 文件
    copy_ffmpeg_binaries();

    // 只添加重新构建触发条件，不进行链接
    if cfg!(target_os = "windows") {
        let manifest_dir = env::var("CARGO_MANIFEST_DIR").unwrap();
        let dll_dir = format!("{}/libs/windows/x64", manifest_dir);
        println!("cargo:rerun-if-changed={}", dll_dir);

        // 也监控根目录的DLL文件
        println!("cargo:rerun-if-changed={}/librtcore.dll", manifest_dir);
        println!("cargo:rerun-if-changed={}/librtssl.dll", manifest_dir);
        println!("cargo:rerun-if-changed={}/librtutils.dll", manifest_dir);
    }

    tauri_build::build()
}

/// 根据目标平台复制预编译的 ffmpeg 二进制文件到输出目录和项目根目录
fn copy_ffmpeg_binaries() {
    let manifest_dir = env::var("CARGO_MANIFEST_DIR").unwrap();
    let out_dir = env::var("OUT_DIR").unwrap();
    let target_dir = Path::new(&out_dir).parent().unwrap().parent().unwrap().parent().unwrap();

    // 确定源目录和文件扩展名
    let (source_subdir, exe_ext) = if cfg!(target_os = "windows") {
        if cfg!(target_arch = "x86_64") {
            ("windows/x64", ".exe")
        } else {
            println!("cargo:warning=Unsupported Windows architecture");
            return;
        }
    } else if cfg!(target_os = "macos") {
        ("macos", "")
    } else if cfg!(target_os = "linux") {
        if cfg!(target_arch = "x86_64") {
            ("linux/x64", "")
        } else if cfg!(target_arch = "aarch64") {
            ("linux/arm64", "")
        } else {
            println!("cargo:warning=Unsupported Linux architecture");
            return;
        }
    } else {
        println!("cargo:warning=Unsupported target OS");
        return;
    };

    let ffmpeg_source_dir = format!("{}/ffmpeg/{}", manifest_dir, source_subdir);
    let ffmpeg_source = format!("{}/ffmpeg{}", ffmpeg_source_dir, exe_ext);
    let ffprobe_source = format!("{}/ffprobe{}", ffmpeg_source_dir, exe_ext);

    // 复制到输出目录（用于运行时）
    let ffmpeg_dest_target = target_dir.join(format!("ffmpeg{}", exe_ext));
    let ffprobe_dest_target = target_dir.join(format!("ffprobe{}", exe_ext));

    // 复制到项目根目录（供 Tauri 打包）
    let ffmpeg_dest_root = Path::new(&manifest_dir).join(format!("ffmpeg{}", exe_ext));
    let ffprobe_dest_root = Path::new(&manifest_dir).join(format!("ffprobe{}", exe_ext));

    // 复制 ffmpeg 到两个位置
    if Path::new(&ffmpeg_source).exists() {
        // 复制到输出目录
        if let Err(e) = fs::copy(&ffmpeg_source, &ffmpeg_dest_target) {
            println!("cargo:warning=Failed to copy ffmpeg to target: {}", e);
        } else {
            println!("cargo:warning=Copied ffmpeg to target: {}", ffmpeg_dest_target.display());
        }

        // 复制到项目根目录（供 Tauri 打包）
        if let Err(e) = fs::copy(&ffmpeg_source, &ffmpeg_dest_root) {
            println!("cargo:warning=Failed to copy ffmpeg to root: {}", e);
        } else {
            println!("cargo:warning=Copied ffmpeg to root: {}", ffmpeg_dest_root.display());
        }
    } else {
        println!("cargo:warning=ffmpeg source not found: {}", ffmpeg_source);
    }

    // 复制 ffprobe 到两个位置
    if Path::new(&ffprobe_source).exists() {
        // 复制到输出目录
        if let Err(e) = fs::copy(&ffprobe_source, &ffprobe_dest_target) {
            println!("cargo:warning=Failed to copy ffprobe to target: {}", e);
        } else {
            println!("cargo:warning=Copied ffprobe to target: {}", ffprobe_dest_target.display());
        }

        // 复制到项目根目录（供 Tauri 打包）
        if let Err(e) = fs::copy(&ffprobe_source, &ffprobe_dest_root) {
            println!("cargo:warning=Failed to copy ffprobe to root: {}", e);
        } else {
            println!("cargo:warning=Copied ffprobe to root: {}", ffprobe_dest_root.display());
        }
    } else {
        println!("cargo:warning=ffprobe source not found: {}", ffprobe_source);
    }

    // 在 Unix 系统上设置执行权限
    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;

        for dest in [&ffmpeg_dest_target, &ffprobe_dest_target, &ffmpeg_dest_root, &ffprobe_dest_root] {
            if dest.exists() {
                if let Ok(metadata) = fs::metadata(dest) {
                    let mut perms = metadata.permissions();
                    perms.set_mode(0o755); // rwxr-xr-x
                    if let Err(e) = fs::set_permissions(dest, perms) {
                        println!("cargo:warning=Failed to set permissions for {}: {}", dest.display(), e);
                    }
                }
            }
        }
    }

    // 添加重新构建触发条件
    println!("cargo:rerun-if-changed={}", ffmpeg_source_dir);
}


