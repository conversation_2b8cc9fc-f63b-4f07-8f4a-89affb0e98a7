{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2937790071811063934, "profile": 2225463790103693989, "path": 18236491560486156021, "deps": [[3060637413840920116, "proc_macro2", false, 17515079266447713286], [3972868919765946583, "serde_derive_internals", false, 2488298445114677622], [17990358020177143287, "quote", false, 13800562964788263047], [18149961000318489080, "syn", false, 16190129315601163247]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars_derive-ae5751b141c568b9\\dep-lib-schemars_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}