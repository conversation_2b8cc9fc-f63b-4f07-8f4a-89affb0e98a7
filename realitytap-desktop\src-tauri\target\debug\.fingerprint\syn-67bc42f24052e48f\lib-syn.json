{"rustc": 10895048813736897673, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 15503613273328313573, "deps": [[1988483478007900009, "unicode_ident", false, 1331469441748295272], [3060637413840920116, "proc_macro2", false, 17515079266447713286], [17990358020177143287, "quote", false, 13800562964788263047]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-67bc42f24052e48f\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}