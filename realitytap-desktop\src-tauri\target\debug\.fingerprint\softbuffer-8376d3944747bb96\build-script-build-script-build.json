{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 1890806518326894489, "deps": [[1884099982326826527, "cfg_aliases", false, 9402735829249278914]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-8376d3944747bb96\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}